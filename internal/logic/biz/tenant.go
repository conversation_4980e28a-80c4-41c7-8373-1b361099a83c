/**
 * @note
 * tenant
 *
 * <AUTHOR>
 * @date 	2025-09-02
 */
package biz

import (
	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/idgen"
	bizModel "gitlab.docsl.com/security/socv2/soc/internal/model/biz"
)

// wrapTenantTableToItem 将TenantTable转换为TenantItem
func wrapTenantTableToItem(tb *bizModel.TenantTable) *TenantItem {
	if tb == nil {
		return &TenantItem{}
	}
	return &TenantItem{
		WalletID:  tb.WalletID,
		Name:      tb.Name,
		Desc:      tb.Desc,
		CreatedAt: tb.CreatedAt,
		UpdatedAt: tb.UpdatedAt,
	}
}

// QueryTenantList 查询平台列表
func QueryTenantList(ctx context.Context, page, perPage int, walletIDs []string, syncStatus *fuseCommon.SyncStatus) (items []*TenantItem, count int64, err error) {
	filter := assetsModel.QueryTenantFilter{
		Page:       page,
		PerPage:    perPage,
		WalletIDs:  walletIDs,
		SyncStatus: syncStatus,
	}
	tbs, err := assetsModel.QueryTenantBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}
	count, err = assetsModel.QueryTenantCountBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, 0, err
	}
	items = make([]*TenantItem, 0, len(tbs))
	for _, tb := range tbs {
		items = append(items, wrapTenantTableToItem(tb))
	}
	return items, count, nil
}

// CreateTenants 创建平台
func CreateTenants(ctx context.Context, inputs []*CreateTenantInput) (err error) {
	tbs := make([]*assetsModel.TenantTable, 0, len(inputs))
	for _, input := range inputs {
		tb := &assetsModel.TenantTable{
			WalletID:   input.WalletID,
			Name:       input.Name,
			Desc:       input.Desc,
			Config:     &input.Config,
			SyncStatus: input.SyncStatus,
		}
		tb.ID = uint(idgen.GetID())
		tbs = append(tbs, tb)
	}
	// 创建Tenants
	_, err = assetsModel.CreateTenants(ctx, tbs)
	if err != nil {
		return err
	}
	return nil
}

// ModifyTenants 修改平台
func ModifyTenants(ctx context.Context, inputs []*ModifyTenantInput) (err error) {
	// 处理Tenant表更新
	for _, input := range inputs {
		// 先查询Tenant是否存在
		Tenants, err := assetsModel.QueryTenantBySeveralConditions(ctx, assetsModel.QueryTenantFilter{
			WalletIDs: []string{input.WalletID},
		})
		if err != nil {
			return err
		}
		if len(Tenants) == 0 {
			return common.ErrRecordNotFound
		}
		err = assetsModel.UpdateTenantByWalletID(ctx, input.WalletID, input.Name, input.Desc, input.Config, input.SyncStatus)
		if err != nil {
			return err
		}
	}
	return nil
}

// DeleteTenants 删除平台
func DeleteTenants(ctx context.Context, walletIDs []string) (err error) {
	// 先查询Tenants是否存在
	Tenants, err := assetsModel.QueryTenantBySeveralConditions(ctx, assetsModel.QueryTenantFilter{
		WalletIDs: walletIDs,
	})
	if err != nil {
		return err
	}
	if len(Tenants) == 0 {
		return common.ErrRecordNotFound
	}
	// 删除Tenant记录
	_, err = assetsModel.DeleteTenantsByWalletIDs(ctx, walletIDs)
	return err
}
