/**
 * @note
 * biz logic structs
 *
 * <AUTHOR>
 * @date 	2025-09-02
 */
package biz

import (
	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/idgen"
	bizModel "gitlab.docsl.com/security/socv2/soc/internal/model/biz"
	"time"
)

// TenantItem 平台信息
type TenantItem struct {
	WalletID   string                `json:"walletID"`
	Name       string                `json:"name"`
	Desc       string                `json:"desc"`
	CreatedAt  time.Time             `json:"createdAt"`
	UpdatedAt  time.Time             `json:"updatedAt"`
}
