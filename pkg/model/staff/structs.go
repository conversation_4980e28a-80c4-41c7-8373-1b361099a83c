package staff

import (
	"gitlab.docsl.com/security/socv2/soc/pkg/common"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
	"gorm.io/gorm"
)

type UserTable struct {
	gorm.Model
	UserName         string                   `gorm:"column:user_name"`           // username，从cas来的
	LarkName         string                   `gorm:"column:lark_name"`           // lark的name
	LarkEnName       string                   `gorm:"column:lark_en_name"`        // lark的enName
	LarkUserID       string                   `gorm:"column:lark_user_id"`        // lark的userID
	LarkLeaderUserID string                   `gorm:"column:lark_leader_user_id"` // lark的leaderUserID
	Mobile           string                   `gorm:"column:mobile"`              // lark的mobile
	Gender           int                      `gorm:"column:gender"`              // 性别
	EmployeeNo       string                   `gorm:"column:employee_no"`         // 员工工号，应该是唯一的
	EmployeeType     int                      `gorm:"column:employee_type"`       // 员工类型
	Email            string                   `gorm:"column:email"`               // email，应该是唯一的
	City             string                   `gorm:"column:city"`                // city
	Country          string                   `gorm:"column:country"`             // country
	JoinTime         int64                    `gorm:"column:join_time"`           // 入职时间
	JobTitle         string                   `gorm:"column:job_title"`           // 职位
	Status           socCommon.UserStatusEnum `gorm:"column:status"`              // 员工状态
}

func (t *UserTable) TableName() string {
	return common.UserTableName
}

func (t *UserTable) OnUpdateColumns() []string {
	return []string{"lark_name", "lark_en_name", "lark_leader_user_id", "mobile", "gender", "employee_no",
		"employee_type", "email", "city", "country",
		"join_time", "job_title", "status"}
}

type DepartmentTable struct {
	gorm.Model
	Name                   string               `json:"name" gorm:"column:name"`
	I18nName               string               `json:"i18nName" gorm:"column:i18n_name"`
	LarkParentDepartmentID string               `json:"larkParentDepartmentID" gorm:"column:lark_parent_department_id"` // lark的string格式
	LarkDepartmentID       string               `json:"larkDepartmentID" gorm:"column:lark_department_id"`              // lark的string格式，此表中为唯一键
	LarkLeaderUserID       string               `json:"larkLeaderUserID" gorm:"column:lark_leader_user_id"`             // lark的string格式
	Order                  string               `json:"order" gorm:"column:order"`
	Status                 socCommon.StatusEnum `json:"status" gorm:"column:status"`
}

func (t *DepartmentTable) TableName() string {
	return common.DepartmentTableName
}

func (t *DepartmentTable) OnUpdateColumns() []string {
	return []string{"name", "i18n_name", "lark_parent_department_id", "lark_leader_user_id", "order", "status"}
}

type UserDepartmentTableRelation struct {
	common.GormModel
	LarkUserID       string `json:"userID" gorm:"column:lark_user_id"`
	LarkDepartmentID string `json:"departmentID" gorm:"column:lark_department_id"`
	UserOrder        int    `json:"userOrder" gorm:"column:user_order"`
	DepartmentOrder  int    `json:"departmentOrder" gorm:"column:department_order"`
	IsPrimaryDept    bool   `json:"isPrimaryDept" gorm:"column:is_primary_dept"` // 是否是主部门
}

func (t *UserDepartmentTableRelation) TableName() string {
	return common.UserDepartmentRelationTableName
}

func (t *UserDepartmentTableRelation) OnUpdateColumns() []string {
	return []string{"user_order", "department_order", "is_primary_dept"}
}
