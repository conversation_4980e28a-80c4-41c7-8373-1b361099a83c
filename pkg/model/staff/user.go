package staff

import (
	"context"
	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/mysql"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

//TODO 加应用内缓存

func GetUserByUserID(ctx context.Context, userID int64) (tb *UserTable, err error) {
	return DefaultService.GetUserByID(ctx, userID)
}

func GetUserByUserIDInAllStatus(ctx context.Context, userID int64) (tb *UserTable, err error) {
	return DefaultService.GetUserByIDInAllStatus(ctx, userID)
}

func GetUserByUserName(ctx context.Context, userName string) (tb *UserTable, err error) {
	return DefaultService.GetUserByUserName(ctx, userName)
}

func GetUsersByLarkNameVague(ctx context.Context, larkName string) (tbs []*UserTable, err error) {
	return DefaultService.GetUsersByLarkNameVague(ctx, larkName)
}

func GetUserByLarkUserID(ctx context.Context, larkUserID string) (tb *UserTable, err error) {
	return DefaultService.GetUserByLarkUserID(ctx, larkUserID)
}

func GetUsersByLarkUserIDs(ctx context.Context, larkUserIDs []string) (tbs []*UserTable, err error) {
	return DefaultService.GetUsersByLarkUserIDs(ctx, larkUserIDs)
}

func GetUserByEmail(ctx context.Context, email string) (tb *UserTable, err error) {
	return DefaultService.GetUserByEmail(ctx, email)
}

func CreateOrUpdateUser(ctx context.Context, users []*UserTable) (err error) {
	return DefaultService.CreateOrUpdateUsers(ctx, users)
}

func UpdateUserNameByID(ctx context.Context, userID int64, userName string) (err error) {
	return DefaultService.UpdateUserNameByID(ctx, userID, userName)
}

type StaffModelImpl struct{}

func (m *StaffModelImpl) getDB(ctx context.Context) (db *gorm.DB, err error) {
	return mysql.GetDB(socCommon.DBName, false, common.GetLogger(ctx))
}

func (m *StaffModelImpl) GetUserByID(ctx context.Context, userID int64) (*UserTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&UserTable{})
	ret := &UserTable{}
	db = db.Where("id = ? and status = ?", userID, socCommon.StatusActivated).First(ret)
	return ret, db.Error
}

func (m *StaffModelImpl) GetUserByIDInAllStatus(ctx context.Context, userID int64) (*UserTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&UserTable{})
	ret := &UserTable{}
	db = db.Where("id = ?", userID).First(ret)
	return ret, db.Error
}

func (m *StaffModelImpl) GetUserByLarkUserID(ctx context.Context, larkUserID string) (*UserTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&UserTable{})
	ret := &UserTable{}
	db = db.Where("lark_user_id = ? and status = ?", larkUserID, socCommon.StatusActivated).First(ret)
	return ret, db.Error
}

func (m *StaffModelImpl) GetUsersByLarkUserIDs(ctx context.Context, larkUserIDs []string) ([]*UserTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&UserTable{})
	ret := make([]*UserTable, 0)
	db = db.Where("lark_user_id in (?) and status = ?", larkUserIDs, socCommon.StatusActivated).Find(&ret)
	return ret, db.Error
}

func (m *StaffModelImpl) GetUserByUserName(ctx context.Context, userName string) (*UserTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&UserTable{})
	ret := &UserTable{}
	db = db.Where("user_name = ? and status = ?", userName, socCommon.StatusActivated).First(ret)
	return ret, db.Error
}

func (m *StaffModelImpl) GetUsersByLarkNameVague(ctx context.Context, larkName string) ([]*UserTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&UserTable{})
	ret := make([]*UserTable, 0)
	db = db.Where("lark_name like ? and status = ?", "%"+larkName+"%", socCommon.StatusActivated).Find(&ret)
	return ret, db.Error
}

func (m *StaffModelImpl) GetUserByEmail(ctx context.Context, email string) (*UserTable, error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return nil, err
	}
	db = db.Model(&UserTable{})
	ret := &UserTable{}
	db = db.Where("email = ? and status = ?", email, socCommon.StatusActivated).First(ret)
	return ret, db.Error
}

func (m *StaffModelImpl) CreateOrUpdateUsers(ctx context.Context, tbs []*UserTable) (err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	t := &UserTable{}
	db = db.Model(&UserTable{})
	// 使用自增ID
	err = db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "lark_user_id"}},       // 主键冲突检测
		DoUpdates: clause.AssignmentColumns(t.OnUpdateColumns()), // 冲突时更新这些字段
	}).CreateInBatches(tbs, 50).Error
	return err
}

func (m *StaffModelImpl) UpdateUserNameByID(ctx context.Context, userID int64, userName string) (err error) {
	db, err := m.getDB(ctx)
	if err != nil {
		return err
	}
	db = db.Model(&UserTable{})
	updateMap := make(map[string]interface{})
	updateMap["user_name"] = userName
	return db.Where("id = ?", userID).Updates(updateMap).Error
}
