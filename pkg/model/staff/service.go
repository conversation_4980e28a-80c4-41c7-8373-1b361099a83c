package staff

import (
	"context"
)

type StaffModel interface {
	// GetUserByUserName 根据UserName查找用户表
	GetUserByUserName(ctx context.Context, userName string) (*UserTable, error)
	// GetUsersByLarkNameVague GetUserByLarkNameVague 根据LarkUserName模糊查找用户表
	GetUsersByLarkNameVague(ctx context.Context, larkName string) ([]*UserTable, error)
	// GetUserByID 根据UserID查找用户表
	GetUserByID(ctx context.Context, userID int64) (*UserTable, error)
	// GetUserByIDInAllStatus 根据UserID查找用户表，包含离职的
	GetUserByIDInAllStatus(ctx context.Context, userID int64) (*UserTable, error)
	// GetUserByLarkUserID 根据larkUserID获取用户
	GetUserByLarkUserID(ctx context.Context, larkUserID string) (*UserTable, error)
	// GetUsersByLarkUserIDs 根据larkUserIDs获取用户
	GetUsersByLarkUserIDs(ctx context.Context, larkUserIDs []string) ([]*UserTable, error)
	// GetUserByEmail 根据email获取用户
	GetUserByEmail(ctx context.Context, email string) (*UserTable, error)
	// CreateOrUpdateUsers 创建或更新用户表
	CreateOrUpdateUsers(ctx context.Context, tbs []*UserTable) (err error)
	// UpdateUserNameByID 更新用户名，用于CAS登录后更新用户信息
	UpdateUserNameByID(ctx context.Context, userID int64, userName string) (err error)

	// CreateOrUpdateDepartments 批量创建或更新部门
	CreateOrUpdateDepartments(ctx context.Context, tbs []*DepartmentTable) (err error)
	GetDepartmentByIDs(ctx context.Context, IDs []int64) ([]*DepartmentTable, error)
	// GetDepartmentByLarkDepartmentIDs 根据Lark部门ID获取部门
	GetDepartmentByLarkDepartmentIDs(ctx context.Context, larkDepartmentIDs []string) ([]*DepartmentTable, error)
	// GetDepartmentByLarkLarkDepartmentID 根据Lark父部门ID获取部门
	GetDepartmentByLarkLarkDepartmentID(ctx context.Context, larkParentDepartmentID string) ([]*DepartmentTable, error)
	// GetAllDepartments 找到所有的部门
	GetAllDepartments(ctx context.Context) ([]*DepartmentTable, error)

	// GetLarkDepartmentRelationsByLarkUserID 根据用户LarkUserID获取用户所在的部门LarkDepartmentID列表
	GetLarkDepartmentRelationsByLarkUserID(ctx context.Context, larkUserID string) ([]*UserDepartmentTableRelation, error)
	// GetLarkDepartmentRelationsByLarkDepartmentID 根据部门LarkDepartmentID获取部门内的用户LarkUserID列表
	GetLarkDepartmentRelationsByLarkDepartmentID(ctx context.Context, larkDepartmentID string) ([]*UserDepartmentTableRelation, error)
	// CreateUserDepartmentRelation 创建用户部门关系
	CreateUserDepartmentRelation(ctx context.Context, tbs []*UserDepartmentTableRelation) (err error)
	// UpdateUserDepartmentRelation 更新用户部门关系
	UpdateUserDepartmentRelation(ctx context.Context, larkUserID, larkDepartmentID string, userOrder, departmentOrder *int, isPrimaryDept *bool) (err error)
	// HardDeleteUserDepartmentRelations 硬删除用户部门关系
	HardDeleteUserDepartmentRelations(ctx context.Context, larkUserID, larkDepartmentID *string) (err error)
	// TruncateUserDepartmentRelations 直接全删
	TruncateUserDepartmentRelations(ctx context.Context) (err error)
}

var DefaultService StaffModel = &StaffModelImpl{}
