/**
 * @note
 * config
 *
 * <AUTHOR>
 * @date 	2025-07-24
 */
package lark

import (
	"context"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/aws"
)

var config SecretConfig

type Config struct {
	AwsSecretID string `json:"awsSecretID"`
	AwsKmsKeyID string `json:"awsKmsKeyID"`
}

type SecretConfig struct {
	AppID             string
	AppSecret         string
	VerificationToken string
	EventEncryptKey   string

	RobotWebhook string
}

func SetupConfig(c Config, decrypt bool) {
	if !decrypt {
		return
	}
	secretConfig, err := aws.GetSecretAndDecrypt(context.Background(), c.AwsSecretID, c.AwsKmsKeyID)
	if err != nil {
		panic("aws.GetSecretAndDecrypt for lark config error: " + err.Error())
	}
	if err = common.JsonStringDecode(secretConfig, &config); err != nil {
		panic("jsonDecode to lark config error: " + err.Error())
	}
}

func GetConfig() SecretConfig {
	return config
}

func GetLarkClient() *lark.Client {
	c := GetConfig()
	return lark.NewClient(c.AppID, c.AppSecret, lark.WithOpenBaseUrl(lark.LarkBaseUrl))
}

const (
	MaxPageSize = 50
)
