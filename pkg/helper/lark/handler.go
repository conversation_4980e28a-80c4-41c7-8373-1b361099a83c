/**
 * @note
 * callback_handler
 *
 * <AUTHOR>
 * @date 	2025-08-05
 */
package lark

import (
	"github.com/kataras/iris/v12"
	larkevent "github.com/larksuite/oapi-sdk-go/v3/event"
	"github.com/larksuite/oapi-sdk-go/v3/event/dispatcher"
	"gitlab.docsl.com/security/common"
	"net/http"
)

func WrapLarkEventDispatcher() *dispatcher.EventDispatcher {
	return dispatcher.NewEventDispatcher(GetConfig().VerificationToken, GetConfig().EventEncryptKey)
}

func NewEventHandlerFunc(eventDispatcher *dispatcher.EventDispatcher, options ...larkevent.OptionFunc) iris.Handler {
	eventDispatcher.InitConfig(options...)
	return func(ctx iris.Context) {
		rawBody, err := ctx.GetBody()
		if err != nil {
			ctx.StatusCode(http.StatusInternalServerError) // 给个500
			_, _ = ctx.WriteString(err.Error())            // 给个500
			return
		}
		eventReq := &larkevent.EventReq{
			Header:     ctx.Request().Header,
			Body:       rawBody,
			RequestURI: ctx.Request().RequestURI,
		}
		// 处理请求
		eventResp := eventDispatcher.Handle(ctx, eventReq)

		// 回写结果
		ctx.ResponseWriter().WriteHeader(eventResp.StatusCode)
		for k, vs := range eventResp.Header {
			for _, v := range vs {
				ctx.ResponseWriter().Header().Add(k, v)
			}
		}

		if len(eventResp.Body) > 0 {
			_, err = ctx.ResponseWriter().Write(eventResp.Body)
			if err != nil {
				common.GetLogger(ctx).Errorf("lark event handler write resp result error:%v", err)
			}
		}
	}
}
