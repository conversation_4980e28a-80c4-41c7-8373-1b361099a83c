/**
 * @note
 * lark
 *
 * <AUTHOR>
 * @date 	2025-07-24
 */
package lark

import (
	"context"
	"errors"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	"github.com/larksuite/oapi-sdk-go/v3/core"
	"github.com/larksuite/oapi-sdk-go/v3/service/im/v1"
	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/http_client"
	"net/http"
)

// 群机器人，这里只是用来发报警的
func SendLarkRobotAlarmMessage(ctx context.Context, content string) error {
	// Simple text message
	message := map[string]interface{}{
		"msg_type": "text",
		"content": map[string]interface{}{
			"text": content,
		},
	}
	payload, err := common.ToPrettyJsonString(message)
	client := http_client.NewHttpClient(common.GetLogger(ctx), false)
	req := client.R()
	resp, err := req.SetResult(&FeishuResponse{}).SetBody(payload).Post(GetConfig().RobotWebhook)
	if err != nil {
		return err
	} else if resp.StatusCode() != http.StatusOK {
		return common.ErrHttpResponsef(resp.Status())
	}
	feishuResp := resp.Result().(*FeishuResponse)
	if feishuResp.Code != 0 {
		return common.ErrHttpResponsef(feishuResp.Msg)
	}
	return nil
}

type FeishuResponse struct {
	Code int         `json:"code"`
	Data interface{} `json:"data"`
	Msg  string      `json:"msg"`
}

func UpdateLarkMessageByMsgID(ctx context.Context, cli *lark.Client, msgID, msgType, content string) (string, error) {
	// 创建请求对象
	req := larkim.NewUpdateMessageReqBuilder().
		MessageId(msgID).
		Body(larkim.NewUpdateMessageReqBodyBuilder().
			MsgType(msgType).
			Content(content).
			Build()).
		Build()
	// 发起请求
	resp, err := cli.Im.Message.Update(ctx, req)

	if err != nil {
		return common.StringEmpty, err
	}
	// 服务端错误处理
	if !resp.Success() {
		return common.StringEmpty, errors.New(larkcore.Prettify(resp.CodeError))
	}
	if resp.Data.MessageId != nil {
		return *resp.Data.MessageId, nil
	} else {
		return common.StringEmpty, nil
	}
}

func SendLarkMessageByUserID(ctx context.Context, cli *lark.Client, userID, uniqID, msgType, content string) (string, error) {
	// 创建请求对象
	req := larkim.NewCreateMessageReqBuilder().
		ReceiveIdType(`user_id`).
		Body(larkim.NewCreateMessageReqBodyBuilder().
			ReceiveId(userID).
			MsgType(msgType).
			Content(content).
			Uuid(uniqID).
			Build()).
		Build()
	// 发起请求
	resp, err := cli.Im.Message.Create(ctx, req)
	if err != nil {
		return common.StringEmpty, err
	}
	// 服务端错误处理
	if !resp.Success() {
		return common.StringEmpty, errors.New(larkcore.Prettify(resp.CodeError))
	}
	if resp.Data.MessageId != nil {
		return *resp.Data.MessageId, nil
	} else {
		return common.StringEmpty, nil
	}
}

func SendLarkMessageByChatID(ctx context.Context, cli *lark.Client, chatID, uniqID, msgType, content string) (string, error) {
	// 创建请求对象
	req := larkim.NewCreateMessageReqBuilder().
		ReceiveIdType(`chat_id`).
		Body(larkim.NewCreateMessageReqBodyBuilder().
			ReceiveId(chatID).
			MsgType(msgType).
			Content(content).
			Uuid(uniqID).
			Build()).
		Build()
	// 发起请求
	resp, err := cli.Im.Message.Create(ctx, req)
	if err != nil {
		return common.StringEmpty, err
	}
	// 服务端错误处理
	if !resp.Success() {
		return common.StringEmpty, errors.New(larkcore.Prettify(resp.CodeError))
	}
	if resp.Data.MessageId != nil {
		return *resp.Data.MessageId, nil
	} else {
		return common.StringEmpty, nil
	}
}

func PatchLarkMessageByMsgID(ctx context.Context, cli *lark.Client, msgID, content string) error {
	// 创建请求对象
	req := larkim.NewPatchMessageReqBuilder().
		MessageId(msgID).
		Body(larkim.NewPatchMessageReqBodyBuilder().
			Content(content).
			Build()).
		Build()
	// 发起请求
	resp, err := cli.Im.Message.Patch(ctx, req)

	if err != nil {
		return err
	}
	// 服务端错误处理
	if !resp.Success() {
		return errors.New(larkcore.Prettify(resp.CodeError))
	}
	return nil
}

func WithdrawMessageByMsgID(ctx context.Context, cli *lark.Client, msgID string) error {
	// 创建请求对象
	req := larkim.NewDeleteMessageReqBuilder().
		MessageId(msgID).
		Build()

	// 发起请求
	resp, err := cli.Im.Message.Delete(ctx, req)

	if err != nil {
		return err
	}
	// 服务端错误处理
	if !resp.Success() {
		return errors.New(larkcore.Prettify(resp.CodeError))
	}
	return nil
}
