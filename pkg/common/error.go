/**
 * @note
 * error
 *
 * <AUTHOR>
 * @date 	2025-02-05
 */
package common

import (
	"errors"
	"fmt"
)

var (
	ErrGenginePoolNotInitialized = errors.New("gengine pool is not initialized")
	ErrTransactionUnitNotUnique  = errors.New("transaction unit is not unique")
	ErrRootStatementNotUnique    = errors.New("root statement is not unique")
	ErrGormScanJsonAssertToBytes = errors.New("gorm scan json type assert to []byte failed")
	ErrParseAmount               = func(amount string) error {
		return fmt.Errorf("parse amount %s failed", amount)
	}
	ErrAwsV4SignatureNotMatched = errors.New("aws v4 signature not matched")
	ErrRuleIDsDoNotExist        = func(missingRuleIDs []int64) error {
		return fmt.Errorf("rules with IDs %v do not exist", missingRuleIDs)
	}
	ErrInvalidNotifyGroup = func(name string) error { return errors.New("invalid notify group: " + name) }
	ErrOptimisticLockFailed = errors.New("optimistic lock failed: record has been modified by another process")
)
