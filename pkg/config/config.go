/**
 * @note
 * config.go
 *
 * <AUTHOR>
 * @date 	2025-01-27
 */
package config

import (
	"gitlab.docsl.com/security/socv2/soc/pkg/helper/lark"
	"os"
	"path/filepath"

	"github.com/BurntSushi/toml"

	"gitlab.docsl.com/security/common/cas"
	"gitlab.docsl.com/security/common/http_server"
	"gitlab.docsl.com/security/common/logger"
	"gitlab.docsl.com/security/common/masker"
	"gitlab.docsl.com/security/common/mysql"
	"gitlab.docsl.com/security/common/prometheus"
	"gitlab.docsl.com/security/common/redis"
	"gitlab.docsl.com/security/common/webauthn"
)

type Env struct {
	BinDir  string
	RootDir string
	ConfDir string
	TmplDir string
}

type Config struct {
	HttpServer http_server.Config `toml:"http_server"`
	Mysql      mysql.Config
	Redis      redis.Config
	Log        logger.Config
	Lark       lark.Config
	Cas        cas.Config
	WebAuthn   webauthn.Config
	Prometheus prometheus.PrometheusConfig
}

var (
	AppEnv    Env
	AppConfig Config
)

// 初始化执行环境
func init() {
	var err error
	AppEnv.BinDir, err = filepath.Abs(filepath.Dir(os.Args[0]))
	if err != nil {
		panic(err)
	}
	AppEnv.RootDir = filepath.Dir(AppEnv.BinDir)
	AppEnv.ConfDir = filepath.Join(AppEnv.RootDir, "/conf")
	AppEnv.TmplDir = filepath.Join(AppEnv.RootDir, "/tmpl")
}

// 加载配置信息
func ReplaceAndLoad(f string, decrypt bool) (string, error) {
	//读取配置文件
	_, err := toml.DecodeFile(f, &AppConfig)
	if err != nil {
		return f, err
	}
	if decrypt {
		AppConfig, err = cryptConfig(AppConfig)
		if err != nil {
			return f, err
		}
	}
	//配置各个模块
	http_server.SetupConfig(AppConfig.HttpServer)
	mysql.SetupConfig(AppConfig.Mysql)
	redis.SetupConfig(AppConfig.Redis)
	logger.SetupConfig(AppConfig.Log)
	cas.SetupConfig(AppConfig.Cas)
	webauthn.SetupConfig(AppConfig.WebAuthn)
	prometheus.SetupPrometheusConfig(AppConfig.Prometheus)
	if http_server.GetConfig().Environment == http_server.EevDev {
		lark.SetupConfig(AppConfig.Lark, false) // 为解决本机调试无法访问aws的问题
	} else {
		lark.SetupConfig(AppConfig.Lark, decrypt)
	}
	return f, nil
}

func cryptConfig(c Config) (Config, error) {
	appConfigIface, err := masker.PropCrypt(AppConfig)
	if err != nil {
		return c, err
	}
	return appConfigIface.(Config), nil
}

func OutputConfigToFile(file string, encrypt bool) (err error) {
	f, err := os.OpenFile(file, os.O_RDWR|os.O_CREATE, 0666)
	defer f.Close()
	if err != nil {
		return err
	}
	encoder := toml.NewEncoder(f)
	out := AppConfig
	if encrypt {
		masker.SetIsEncrypt(true)
		out, err = cryptConfig(AppConfig)
		if err != nil {
			return err
		}
		masker.SetIsEncrypt(false)
	}

	if err = encoder.Encode(out); err != nil {
		return err
	}
	return
}
