[http_server]
  Environment = "test"
  ServeAddr = ":10002"
  InterAddr = ":10001"
  PProfAddr = ":10188"
  Debug = true
  AllowOrigins = ["*"]

[Mysql]
  [Mysql.audit]
    [Mysql.audit.RW]
      DataSourceName = "7m8J0YSwm8Y9SQ7plDmBHMIUnBd3AcKe8+SHVyiLTvLMiWfr3E3fCMi79BP3NchIdW7c6eCp802DeUCBCpCA1JKt0gJ1mzzbE/KpxT4PbaU1Q+6+JsgihxuYhpSkTR10YB3ueRpEuk6enmWXqsVrciATRqtRvW4RM1qqnk50kr8wUrRFHFkI1+RbJUitCFwVRPxWGybDL6stbHm+esQA1AjwI7doKFfQMEsd0G2Fgrk="
      MaxIdleConns = 100
      MaxOpenConns = 200
      MaxRetryTimes = 1
    [Mysql.audit.R]
      DataSourceName = "7m8J0YSwm8Y9SQ7plDmBHMIUnBd3AcKe8+SHVyiLTvLMiWfr3E3fCMi79BP3NchIdW7c6eCp802DeUCBCpCA1JKt0gJ1mzzbE/KpxT4PbaU1Q+6+JsgihxuYhpSkTR10YB3ueRpEuk6enmWXqsVrciATRqtRvW4RM1qqnk50kr8wUrRFHFkI1+RbJUitCFwVRPxWGybDL6stbHm+esQA1AjwI7doKFfQMEsd0G2Fgrk="
      MaxIdleConns = 100
      MaxOpenConns = 200
      MaxRetryTimes = 1

[Redis]
  [Redis.default]
    Servers = ["**********:6379"]
    MaxRetries = -1

[Log]
  LogLevel = "info"
  LogFile = "/root/logs/fuse.log"
  KafkaLog = false
  LocalLog = true
  [Log.Kafka]
    Servers = ["127.0.0.1:9092"]
    Topic = "audit-log"
    InjectHostname = true
    App = "audit"
    AppName = "audit"
    EnvName = "test"
  [Log.Rotate]
    Interval = 24
    MaxAge = 30
    MaxSize = 15360
    LocalTime = true

[Cas]
  EndPoint = "http://localhost"
  FrontDomain = "http://localhost"
  ClientID = "WkmWcZTmfTEKWXBBaJI1Mc3VWDBm7L66eDhj9ULp1Go="
  ClientSecret = "u4Go8BYonMODN5nb376jZo48Qpur74yZ0HTcOVqwYuues7MPfofto8pkxa521fKw"
  Cert = "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"
  EnforcerID = ""
  OrgName = "built-in"
  AppName = "audit"
  AppDomain = ""
  LogoutPath = "/api/user/logout"
  CallbackPath = "/api/user/callback"
  NoValidateSSOPath = ["/"]
  NoValidateIAMPath = ["/"]
  CookieKey = "_q"
  SessionKey = "audit_session_%s"
  SessionTTL = 86400

[WebAuthn]
  RPDisplayName = "audit"
  RPID = "audit.sec-test.yorkapp.com"
  RPOrigins = ["https://audit.sec-test.yorkapp.com"]

[Notify]
  [Notify.alarm]
    TeamsWebhookUrl = "https://prod-13.southeastasia.logic.azure.com:443/workflows/77852765f7cf4a37bc70df4f09ec437e/triggers/manual/paths/invoke?api-version=2016-06-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=N7GMiC0ZGcit3nay-TpWy_b_USbuHvwqqCRcxIjzB9k"
    FeishuWebhookUrl = "https://open.feishu.cn/open-apis/bot/v2/hook/12bf673b-2599-4faa-8861-7d775b7f67ac"
    Interval = 180
    SmsNumbers = ["+8618519930516"]

[Prometheus]
  Namespace = "sec"
  Subsystem = "audit_test"
  Addr = ":7788"
